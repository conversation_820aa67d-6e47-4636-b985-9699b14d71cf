using Microsoft.AspNetCore.Mvc;
using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Application.Services;

namespace Zin.Logging.API.Controllers;

[ApiController]
[Route("[controller]")]
public class LogsController : ControllerBase
{
    private readonly LogService _logService;

    public LogsController(LogService logService)
    {
        _logService = logService;
    }

    [HttpPost]
    public async Task<IActionResult> Post(LogBatchDto logBatch)
    {
        if (logBatch?.Events == null || !logBatch.Events.Any())
        {
            return BadRequest(new { error = "Events array is required and cannot be empty" });
        }

        var response = await _logService.SaveLogBatch(logBatch);

        if (response.Rejected > 0 && response.Accepted == 0)
        {
            return BadRequest(response);
        }

        if (response.Rejected > 0)
        {
            return StatusCode(207, response);
        }

        return Ok(response);
    }

    [HttpPost("legacy")]
    public async Task<IActionResult> PostLegacy(IEnumerable<LogInputDto> logInputs)
    {
        await _logService.SaveLogs(logInputs);
        return Ok();
    }

    [HttpGet]
    public async Task<IActionResult> Get([FromQuery] int page = 1, [FromQuery] int pageSize = 100)
    {
        var logs = await _logService.GetLogsAsync(page, pageSize);
        return Ok(logs);
    }
}
