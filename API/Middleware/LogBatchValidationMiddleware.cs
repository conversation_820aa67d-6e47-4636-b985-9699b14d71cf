using System.Text.Json;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.API.Middleware;

public class LogBatchValidationMiddleware
{
    private readonly RequestDelegate _next;

    public LogBatchValidationMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (ShouldValidate(context))
        {
            var validationResult = await ValidateLogBatch(context);
            if (validationResult != null)
            {
                await WriteErrorResponse(context, validationResult);
                return;
            }
        }

        await _next(context);
    }

    private static bool ShouldValidate(HttpContext context)
    {
        return context.Request.Method == "POST" && 
               context.Request.Path.StartsWithSegments("/logs") &&
               !context.Request.Path.StartsWithSegments("/logs/legacy");
    }

    private static async Task<object?> ValidateLogBatch(HttpContext context)
    {
        try
        {
            context.Request.EnableBuffering();
            var body = await new StreamReader(context.Request.Body).ReadToEndAsync();
            context.Request.Body.Position = 0;

            if (string.IsNullOrWhiteSpace(body))
                return new { error = "Request body is required" };

            var logBatch = JsonSerializer.Deserialize<LogBatchDto>(body, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            if (logBatch?.Events == null || !logBatch.Events.Any())
                return new { error = "Events array is required and cannot be empty" };

            return null;
        }
        catch (JsonException)
        {
            return new { error = "Invalid JSON format" };
        }
    }

    private static async Task WriteErrorResponse(HttpContext context, object error)
    {
        context.Response.StatusCode = 400;
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(error));
    }
}
