using System.Text.Json;
using Zin.Logging.Application.DTOs;

namespace Zin.Logging.API.Middleware;

public class LogBatchResponseMiddleware
{
    private readonly RequestDelegate _next;

    public LogBatchResponseMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (ShouldIntercept(context))
        {
            var originalBodyStream = context.Response.Body;
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            await _next(context);

            await ProcessLogBatchResponse(context, originalBodyStream, responseBody);
        }
        else
        {
            await _next(context);
        }
    }

    private static bool ShouldIntercept(HttpContext context)
    {
        return context.Request.Method == "POST" && 
               context.Request.Path.StartsWithSegments("/logs") &&
               !context.Request.Path.StartsWithSegments("/logs/legacy");
    }

    private static async Task ProcessLogBatchResponse(HttpContext context, Stream originalBodyStream, MemoryStream responseBody)
    {
        responseBody.Seek(0, SeekOrigin.Begin);
        var responseText = await new StreamReader(responseBody).ReadToEndAsync();

        if (context.Response.StatusCode == 200 && !string.IsNullOrEmpty(responseText))
        {
            try
            {
                var response = JsonSerializer.Deserialize<LogBatchResponseDto>(responseText, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (response != null)
                {
                    var newStatusCode = DetermineStatusCode(response);
                    context.Response.StatusCode = newStatusCode;
                }
            }
            catch (JsonException)
            {
                // Se não conseguir deserializar, mantém a resposta original
            }
        }

        responseBody.Seek(0, SeekOrigin.Begin);
        await responseBody.CopyToAsync(originalBodyStream);
    }

    private static int DetermineStatusCode(LogBatchResponseDto response)
    {
        return (response.Accepted, response.Rejected) switch
        {
            (0, > 0) => 400, // All rejected
            (> 0, > 0) => 207, // Partial success
            _ => 200 // All accepted
        };
    }
}
