using Zin.Logging.Application.DTOs;
using Zin.Logging.Shared.DTOs;
using Zin.Logging.Domain.Entities;
using Zin.Logging.Domain.Interfaces;
using Zin.Logging.Shared.Utils;

namespace Zin.Logging.Application.Services;

public class LogService
{
    private readonly ILogRepository _logRepository;
    private readonly PiiMasker _piiMasker;

    public LogService(ILogRepository logRepository, PiiMasker piiMasker)
    {
        _logRepository = logRepository;
        _piiMasker = piiMasker;
    }

    public async Task SaveLogs(IEnumerable<LogInputDto> logInputs)
    {
        var logEvents = logInputs.Select(logInput =>
        {
            PiiMasker.Mask(logInput);
            return new LogEvent
            {
                EventId = Guid.NewGuid(),
                Ts = logInput.Ts,
                Level = logInput.Level,
                Message = logInput.Message,
                Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                Exception = logInput.Exception != null ? new Domain.Entities.ExceptionInfo
                {
                    Type = logInput.Exception.Type,
                    Message = logInput.Exception.Message,
                    Stack = logInput.Exception.Stack
                } : null,
                Trace = logInput.Trace != null ? new Domain.Entities.TraceInfo
                {
                    CorrelationId = logInput.Trace.CorrelationId,
                    TraceId = logInput.Trace.TraceId,
                    SpanId = logInput.Trace.SpanId
                } : null,
                Tenant = logInput.Tenant,
                Host = logInput.Host,
                SchemaVersion = 1
            };
        });

        await _logRepository.AddLogsAsync(logEvents);
    }

    public async Task<LogBatchResponseDto> SaveLogBatch(LogBatchDto logBatch)
    {
        var response = new LogBatchResponseDto
        {
            Errors = new List<LogErrorDto>()
        };

        var validLogs = new List<LogEvent>();
        var index = 0;

        foreach (var logInput in logBatch.Events)
        {
            try
            {
                var validationError = ValidateLogInput(logInput);
                if (!string.IsNullOrEmpty(validationError))
                {
                    response.Errors.Add(new LogErrorDto
                    {
                        Index = index,
                        Error = validationError
                    });
                    response.Rejected++;
                    index++;
                    continue;
                }

                PiiMasker.Mask(logInput);

                var logEvent = new LogEvent
                {
                    EventId = Guid.NewGuid(),
                    Ts = logInput.Ts,
                    Level = logInput.Level,
                    Message = logInput.Message,
                    Props = logInput.Props?.ToDictionary(p => p.Key, p => BsonConverter.ConvertJsonElement(p.Value)),
                    Exception = logInput.Exception != null ? new Domain.Entities.ExceptionInfo
                    {
                        Type = logInput.Exception.Type,
                        Message = logInput.Exception.Message,
                        Stack = logInput.Exception.Stack
                    } : null,
                    Trace = logInput.Trace != null ? new Domain.Entities.TraceInfo
                    {
                        CorrelationId = logInput.Trace.CorrelationId,
                        TraceId = logInput.Trace.TraceId,
                        SpanId = logInput.Trace.SpanId
                    } : null,
                    Tenant = logInput.Tenant,
                    Host = logInput.Host,
                    SchemaVersion = 1
                };

                validLogs.Add(logEvent);
                response.Accepted++;
            }
            catch (Exception ex)
            {
                response.Errors.Add(new LogErrorDto
                {
                    Index = index,
                    Error = $"Processing error: {ex.Message}"
                });
                response.Rejected++;
            }

            index++;
        }

        if (validLogs.Any())
        {
            try
            {
                await _logRepository.AddLogsAsync(validLogs);
            }
            catch (Exception ex)
            {
                response.Rejected += response.Accepted;
                response.Accepted = 0;
                response.Errors.Add(new LogErrorDto
                {
                    Index = -1,
                    Error = $"Database error: {ex.Message}"
                });
            }
        }

        if (!response.Errors.Any())
        {
            response.Errors = null;
        }

        return response;
    }

    private static string? ValidateLogInput(LogInputDto logInput)
    {
        if (logInput.Ts == default)
            return "Timestamp is required";

        if (string.IsNullOrWhiteSpace(logInput.Level))
            return "Level is required";

        if (string.IsNullOrWhiteSpace(logInput.Message))
            return "Message is required";

        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        if (!validLevels.Contains(logInput.Level, StringComparer.OrdinalIgnoreCase))
            return $"Invalid log level. Valid levels: {string.Join(", ", validLevels)}";

        return null;
    }

    public async Task<IEnumerable<LogOutputDto>> GetLogsAsync(int page, int pageSize)
    {
        var logEvents = await _logRepository.GetLogsAsync(page, pageSize);
        return logEvents.Select(logEvent => new LogOutputDto
        {
            EventId = logEvent.EventId,
            Ts = logEvent.Ts,
            Level = logEvent.Level,
            Message = logEvent.Message,
            Props = logEvent.Props,
            Tenant = logEvent.Tenant,
            Host = logEvent.Host
        });
    }
}
