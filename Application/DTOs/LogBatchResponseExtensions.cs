using Microsoft.AspNetCore.Mvc;

namespace Zin.Logging.Application.DTOs;

public static class LogBatchResponseExtensions
{
    public static IActionResult ToHttpResult(this LogBatchResponseDto response)
    {
        return response switch
        {
            { Accepted: 0, Rejected: > 0 } => new BadRequestObjectResult(response),
            { Rejected: > 0 } => new ObjectResult(response) { StatusCode = 207 }, // Multi-Status for partial success
            _ => new OkObjectResult(response)
        };
    }
}
