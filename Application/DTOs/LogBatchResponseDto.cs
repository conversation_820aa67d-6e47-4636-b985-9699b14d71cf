namespace Zin.Logging.Application.DTOs;

public class LogBatchResponseDto
{
    public int Accepted { get; set; }
    public int Rejected { get; set; }
    public List<LogErrorDto>? Errors { get; set; }

    public int HttpStatusCode => (Accepted, Rejected) switch
    {
        (0, > 0) => 400, // All rejected
        (> 0, > 0) => 207, // Partial success
        _ => 200 // All accepted
    };
}

public class LogErrorDto
{
    public int Index { get; set; }
    public string Error { get; set; } = string.Empty;
    public string? Field { get; set; }
}
