using System.ComponentModel.DataAnnotations;
using Zin.Logging.Shared.Validation;

namespace Zin.Logging.Shared.DTOs;

public class LogInputDto
{
    [Required(ErrorMessage = "Timestamp is required")]
    public DateTime Ts { get; set; }

    [Required(ErrorMessage = "Level is required")]
    [ValidLogLevel]
    public string? Level { get; set; }

    [Required(ErrorMessage = "Message is required")]
    public string? Message { get; set; }

    public Dictionary<string, object>? Props { get; set; }
    public ExceptionInfoDto? Exception { get; set; }
    public TraceInfoDto? Trace { get; set; }
    public string? Tenant { get; set; }
    public string? Host { get; set; }
}

public class ExceptionInfoDto
{
    public string? Type { get; set; }
    public string? Message { get; set; }
    public string? Stack { get; set; }
}

public class TraceInfoDto
{
    public string? CorrelationId { get; set; }
    public string? TraceId { get; set; }
    public string? SpanId { get; set; }
}
