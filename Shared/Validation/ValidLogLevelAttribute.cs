using System.ComponentModel.DataAnnotations;

namespace Zin.Logging.Shared.Validation;

public class ValidLogLevelAttribute : ValidationAttribute
{
    private static readonly string[] ValidLevels = { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };

    public override bool IsValid(object? value)
    {
        if (value is not string level)
            return false;

        return ValidLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    public override string FormatErrorMessage(string name)
    {
        return $"Invalid log level. Valid levels: {string.Join(", ", ValidLevels)}";
    }
}
